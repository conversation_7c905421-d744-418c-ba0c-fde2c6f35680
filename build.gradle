plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.2'
    id 'io.spring.dependency-management' version '1.1.4'
    id 'jacoco'  // For code coverage
    id 'checkstyle' // For code style checks
    id 'idea' // Add IDE support
}

group = 'com.xcess'
version = '1.0.0'
sourceCompatibility = '17'
description = 'Demo project for Spring Boot'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:2023.0.0"
    }
}

dependencies {
    // Spring Boot Starters
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    // Eureka client dependencies
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'

    // Kafka dependencies
    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'org.apache.kafka:kafka-clients'

    // Jakarta dependencies for Spring Boot 3
    implementation 'jakarta.annotation:jakarta.annotation-api'

    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    // OpenAPI Documentation
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.3.0'
    
    // Database
    runtimeOnly 'com.mysql:mysql-connector-j'
    implementation 'org.liquibase:liquibase-core'
    
    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    
    // MapStruct
    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
    
    // OpenAPI/Swagger
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.3.0'
    
    // Jackson for JSON
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.10.1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.10.1'
    testImplementation 'org.mockito:mockito-core:5.8.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.8.0'

    // Apache POI for Excel file handling
    implementation 'org.apache.poi:poi:5.2.3'
    implementation 'org.apache.poi:poi-ooxml:5.2.3'
    
    // OpenCSV for CSV file handling
    implementation 'com.opencsv:opencsv:5.7.1'

    // Hibernate types
    implementation 'com.vladmihalcea:hibernate-types-60:2.21.1'

}

tasks.named('bootJar') {
    archiveBaseName = 'ocs'
    archiveVersion = project.version
}

tasks.named('jar') {
    enabled = false
}

tasks.named('test') {
    useJUnitPlatform()
    finalizedBy jacocoTestReport
}

jacoco {
    toolVersion = "0.8.11"
}

jacocoTestReport {
    dependsOn test
    reports {
        xml.required = true
        html.required = true
    }
}

tasks.named('compileJava') {
    options.compilerArgs << '-parameters'
    options.encoding = 'UTF-8'
}

tasks.named('processResources') {
    filesMatching('application.properties') {
        expand(project.properties)
    }
}

// Add IDE-specific settings
idea {
    module {
        downloadJavadoc = true
        downloadSources = true
    }
} 