<<<<<<< Updated upstream
#Start with a base image containing Java runtime
FROM openjdk:17-jdk-slim

# Add the application's jar to the image
COPY build/libs/ocs-1.0.0.jar ocs-1.0.0.jar

# expose port 8060
EXPOSE 8060

# execute the application
ENTRYPOINT ["java", "-jar", "ocs-1.0.0.jar"]
=======
FROM eclipse-temurin:17-jdk as build
WORKDIR /app

# Copy gradle files and download dependencies
COPY gradlew .
COPY gradle gradle
COPY build.gradle .
COPY settings.gradle .
RUN chmod +x gradlew
RUN ./gradlew dependencies --no-daemon

# Copy source code
COPY src src

# Build the application
RUN ./gradlew bootJar --no-daemon

# Runtime stage
FROM eclipse-temurin:17-jre
WORKDIR /app

# Copy the JAR file from the build stage
COPY --from=build /app/build/libs/ocs-1.0.0.jar app.jar

# Expose the port the app runs on
EXPOSE 8060

# Run the application
ENTRYPOINT ["java", "-jar", "app.jar"]
>>>>>>> Stashed changes
