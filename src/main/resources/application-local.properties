spring.application.name=ocs
server.port=8080

spring.datasource.url=*******************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=update

cache.refresh.interval=100000

# Kafka Consumer Configuration
spring.kafka.bootstrap-servers=localhost:9092
kafka.topic.refresh.interval=300000
spring.kafka.consumer.group-id=ocs-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.listener.concurrency=3
spring.kafka.consumer.properties.spring.json.trusted.packages=*

spring.kafka.producer.bootstrap-servers=localhost:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer

spring.jackson.mapper.ACCEPT_CASE_INSENSITIVE_ENUMS=true

spring.liquibase.change-log=classpath:/db/changelog/db.changelog-master.xml
# Liquibase configuration for local development
spring.liquibase.parameters.ignore-checksums=true
#spring.liquibase.drop-first=false
#spring.liquibase.clear-checksums=true

# Content negotiation configuration
spring.mvc.contentnegotiation.favor-parameter=false
spring.mvc.contentnegotiation.media-types.json=application/json
# Remove these deprecated properties
# spring.http.encoding.charset=UTF-8
# spring.http.encoding.enabled=true
# spring.http.encoding.force=true

# Replace with these new properties
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

springdoc.swagger-ui.operations-sorter=alpha
springdoc.swagger-ui.tags-sorter=alpha
management.endpoints.web.exposure.include=*
management.endpoints.web.base-path=/actuator

# JWT Configuration
jwt.secret=your_strong_secret_key_here_minimum_32_characters_long_for_security
jwt.expiration.ms=86400000

#logging.level.org.springframework.boot.actuate=DEBUG
logging.level.root=INFO
logging.level.com.xcess.ocs=DEBUG
logging.level.org.springframework=INFO
#logging.level.org.hibernate=INFO
logging.level.org.hibernate.SQL=OFF

