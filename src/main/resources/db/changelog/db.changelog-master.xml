<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.10.xsd">

    <!-- ChangeSet 1: Create the 'countries' table -->
    <changeSet id="1" author="system">
        <preConditions onFail="MARK_RAN" onError="CONTINUE">
            <not>
                <tableExists tableName="countries"/>
            </not>
        </preConditions>

        <createTable tableName="countries">
            <column name="country_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="country_code" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <!-- ChangeSet 2: Load data from countries.csv -->
    <changeSet id="2" author="system">
        <preConditions onFail="MARK_RAN" onError="CONTINUE">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM countries
            </sqlCheck>
        </preConditions>

        <loadData tableName="countries"
                  file="classpath:db/data/countries.csv"
                  separator=","
                  encoding="UTF-8"
                  relativeToChangelogFile="false">
            <column name="name" type="STRING"/>
            <column name="country_code" type="STRING"/>
        </loadData>
    </changeSet>

    <include file="db/changelog/source-configuration.xml" />

    <!-- Include Source_CDR_Configuration changelog -->
    <include file="db/changelog/source-cdr-configuration.xml" />

    <!-- Include Rated_CDR changelog -->
    <include file="db/changelog/ratedcdr.xml" />
    
    <!-- Include Unrated_CDR changelog -->
    <include file="db/changelog/unratedcdr.xml" />
    
    <!-- Remove rating status columns from Rated_CDR -->
<!--    <include file="db/changelog/remove-rating-status.xml" />-->
</databaseChangeLog>
