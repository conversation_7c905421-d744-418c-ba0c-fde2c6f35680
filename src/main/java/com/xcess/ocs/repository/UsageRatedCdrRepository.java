package com.xcess.ocs.repository;

import com.xcess.ocs.entity.UsageRatedCdr;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface UsageRatedCdrRepository extends JpaRepository<UsageRatedCdr, Long>,
        JpaSpecificationExecutor<UsageRatedCdr> {
    // Add usage-specific query methods here as needed
}