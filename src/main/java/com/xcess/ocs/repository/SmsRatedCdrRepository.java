package com.xcess.ocs.repository;

import com.xcess.ocs.entity.SmsRatedCdr;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface SmsRatedCdrRepository extends JpaRepository<SmsRatedCdr, Long>,
        JpaSpecificationExecutor<SmsRatedCdr> {
    // Add SMS-specific query methods here as needed
}