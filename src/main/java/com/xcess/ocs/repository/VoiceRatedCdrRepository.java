package com.xcess.ocs.repository;

import com.xcess.ocs.entity.VoiceRatedCdr;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface VoiceRatedCdrRepository extends JpaRepository<VoiceRatedCdr, Long>,
        JpaSpecificationExecutor<VoiceRatedCdr> {
    // Add voice-specific query methods here as needed
}