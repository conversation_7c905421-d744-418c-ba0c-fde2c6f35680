package com.xcess.ocs.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import jakarta.persistence.Id;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import com.xcess.ocs.service.RatedCdrServiceContext;
import com.xcess.ocs.dto.RatedCdrDTO;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sms_rated_cdr")
public class SmsRatedCdr extends RatedCdr {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "sms_cdr_id")
    private Long smsCdrId;

    @Column(name = "message_segments")
    private Integer messageSegments;

    @Column(name = "total_cost", precision = 10, scale = 4)
    private BigDecimal totalCost;

    @Override
    public ServiceType getServiceType() {
        return ServiceType.SMS;
    }

    @Override
    public RatedCdr save(RatedCdrServiceContext context) {
        return context.smsRatedCdrService().saveSmsRatedCdr(this);
    }

    @Override
    public RatedCdrDTO mapToDto(RatedCdrServiceContext context) {
        return context.smsRatedCdrService().mapToDto(this);
    }
}