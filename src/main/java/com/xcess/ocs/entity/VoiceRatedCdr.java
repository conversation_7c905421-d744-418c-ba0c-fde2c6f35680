package com.xcess.ocs.entity;

import com.xcess.ocs.service.RatedCdrServiceContext;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import jakarta.persistence.Id;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import com.xcess.ocs.dto.RatedCdrDTO;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "voice_rated_cdr")
public class VoiceRatedCdr extends RatedCdr {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "voice_cdr_id")
    private Long voiceCdrId;


    @Column(name = "calling_number")
    private String callingNumber;
    @Column(name = "called_number")
    private String calledNumber;

    @Column(name = "start_time")
    private String startTime;
    @Column(name = "end_time")
    private String endTime;
    @Column(name = "incoming_account_id")
    private String incomingAccountId;
    @Column(name = "outgoing_account_id")
    private String outgoingAccountId;
    @Column(name = "incoming_applied_rate")
    private BigDecimal incomingAppliedRate;
    @Column(name = "outgoing_applied_rate")
    private BigDecimal outgoingAppliedRate;
    @Column(name = "incoming_rate_package_id")
    private Long incomingRatePackageId;
    @Column(name = "outgoing_rate_package_id")
    private Long outgoingRatePackageId;
    @Column(name = "incoming_rate_package_name")
    private String incomingRatePackageName;
    @Column(name = "incoming_rate_detail_id")
    private Long incomingRateDetailId;
    @Column(name = "incoming_matched_source_prefix")
    private String incomingMatchedSourcePrefix;
    @Column(name = "incoming_matched_destination_prefix")
    private String incomingMatchedDestinationPrefix;
    @Column(name = "incoming_source_destination_match")
    private Boolean incomingSourceDestinationMatch;
    @Column(name = "outgoing_rate_package_name")
    private String outgoingRatePackageName;
    @Column(name = "outgoing_rate_detail_id")
    private Long outgoingRateDetailId;
    @Column(name = "outgoing_matched_source_prefix")
    private String outgoingMatchedSourcePrefix;
    @Column(name = "outgoing_matched_destination_prefix")
    private String outgoingMatchedDestinationPrefix;
    @Column(name = "outgoing_source_destination_match")
    private Boolean outgoingSourceDestinationMatch;
    @Column(name = "incoming_account_type")
    private String incomingAccountType;
    @Column(name = "outgoing_account_type")
    private String outgoingAccountType;

    @Column(name = "duration_minutes", precision = 10, scale = 2)
    private BigDecimal durationMinutes;

    @Column(name = "total_cost", precision = 10, scale = 4)
    private BigDecimal totalCost;

    @Override
    public ServiceType getServiceType() {
        return ServiceType.VOICE;
    }

    @Override
    public RatedCdr save(RatedCdrServiceContext context) {
        return context.voiceRatedCdrService().saveVoiceRatedCdr(this);
    }

    @Override
    public RatedCdrDTO mapToDto(RatedCdrServiceContext context) {
        return context.voiceRatedCdrService().mapToDto(this);
    }
}