package com.xcess.ocs.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import jakarta.persistence.Id;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import com.xcess.ocs.service.RatedCdrServiceContext;
import com.xcess.ocs.dto.RatedCdrDTO;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "usage_rated_cdr")
public class UsageRatedCdr extends RatedCdr {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "usage_cdr_id")
    private Long usageCdrId;

    @Column(name = "user_number")
    private Long userNumber;

    @Column(name = "data_usage")
    private Long dataUsage;
    @Column(name = "session_start_time")
    private Long sessionStartTime;

    @Column(name = "session_end_time")
    private Long sessionEndTime;

    @Column(name = "total_cost", precision = 10, scale = 4)
    private BigDecimal totalRate;

    @Override
    public ServiceType getServiceType() {
        return ServiceType.USAGE;
    }

    @Override
    public RatedCdr save(RatedCdrServiceContext context) {
        return context.usageRatedCdrService().saveUsageRatedCdr(this);
    }

    @Override
    public RatedCdrDTO mapToDto(RatedCdrServiceContext context) {
        return context.usageRatedCdrService().mapToDto(this);
    }
}