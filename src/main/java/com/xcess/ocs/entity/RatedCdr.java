package com.xcess.ocs.entity;

import com.xcess.ocs.service.RatedCdrServiceContext;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.xcess.ocs.dto.RatedCdrDTO;

@Data
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
public abstract class RatedCdr extends BaseEntity {
    @Column(name = "source_id")
    private Long sourceId;

    public abstract ServiceType getServiceType();

    public abstract RatedCdr save(RatedCdrServiceContext context);

    public abstract RatedCdrDTO mapToDto(RatedCdrServiceContext context);

}