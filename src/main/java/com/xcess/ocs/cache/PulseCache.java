package com.xcess.ocs.cache;

import com.xcess.ocs.dto.PulseDTO;
import com.xcess.ocs.entity.Pulse;
import com.xcess.ocs.mapper.PulseMapper;
import com.xcess.ocs.repository.PulseRepository;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@RequiredArgsConstructor
public class PulseCache {

    private final PulseRepository pulseRepository;
    private final PulseMapper pulseMapper;

    // Cache to store all PulseDTOs by ID
    private final Map<Long, PulseDTO> pulseCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void preloadCache() {
        log.info("Preloading pulse cache...");
        List<Pulse> pulses = pulseRepository.findAll();
        for (Pulse pulse : pulses) {
            PulseDTO dto = PulseMapper.toDTO(pulse);
            pulseCache.put(pulse.getPulseId(), dto);
        }
        log.debug("Preloaded {} pulses into cache", pulseCache.size());
        log.info("{} Pulses", pulseCache.size());
    }

    public List<PulseDTO> getAllPulses() {
        log.debug("Fetching all pulses from cache");
        return new ArrayList<>(pulseCache.values());
    }

    public PulseDTO getPulseById(Long id) {
        log.debug("Fetching pulse with ID: {} from cache", id);
        return pulseCache.get(id);
    }

    public void addToCache(PulseDTO dto) {
        log.debug("Adding pulse ID: {} to cache", dto.getPulseId());
        pulseCache.put(dto.getPulseId(), dto);
    }

    public void updateCache(PulseDTO dto) {
        log.debug("Updating cache for pulse ID: {}", dto.getPulseId());
        pulseCache.put(dto.getPulseId(), dto);
    }

    public void removeFromCache(Long id) {
        log.debug("Removing pulse ID: {} from cache", id);
        pulseCache.remove(id);
    }

    @Scheduled(fixedDelayString = "${cache.refresh.interval:600000}")
    public void refreshCache() {
        log.info("Refreshing pulse cache...");
        pulseCache.clear();
        preloadCache();
    }
}
