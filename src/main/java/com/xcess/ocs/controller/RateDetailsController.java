package com.xcess.ocs.controller;

import com.xcess.ocs.constants.ResponseConstants;
import com.xcess.ocs.dto.*;
import com.xcess.ocs.service.RateDetailsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.HttpStatus;
import com.xcess.ocs.exception.ResourceNotFoundException;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/rate-details")
@Tag(name = "6. Rate Details", description = "Endpoints for managing rate details")
public class RateDetailsController {
    private final RateDetailsService rateDetailsService;

    public RateDetailsController(RateDetailsService rateDetailsService) {
        this.rateDetailsService = rateDetailsService;
    }

    @Operation(summary = "Create a new rate detail", description = "Creates a new rate detail after entering RateDetailDTO")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "HTTP Status OK"),
            @ApiResponse(responseCode = "404", description = "HTTP Status NOT FOUND", content = @Content(schema = @Schema(implementation = ErrorResponseDTO.class)))
    })
    @PostMapping("{ratePackageId}")
    public ResponseEntity<?> createRateDetail(
            @PathVariable("ratePackageId") Long ratePackageId,
            @RequestParam(name = "type", required = true) String type,
            @RequestBody List<RateDetailDTO> rateDetailDTOs) {
        log.info("Received create request with type '{}'", type);

        if (!type.equalsIgnoreCase("replace")) {
            return ResponseEntity.badRequest().body("Invalid operation type. Only 'replace' is supported for now.");
        }
        log.info("Creating new rate details for rate package ID: {}", ratePackageId);

        List<RateDetailDTO> createdDetails = rateDetailsService.createRateDetail(ratePackageId,rateDetailDTOs);

        log.info("Rate details created successfully");
        return ResponseEntity.status(HttpStatus.CREATED).body(createdDetails);
    }

    @Operation(summary = "Get all rate details", description = "Returns a list of all rate details")
    @ApiResponse(responseCode = "200", description = "HTTP Status OK")
    @GetMapping
    public ResponseEntity<List<RateDetailDTO>> getAllRateDetails() {
        log.info("Fetching all rate details");
        List<RateDetailDTO> rateDetails = rateDetailsService.getAllRateDetails();
        log.info("Retrieved {} rate details", rateDetails.size());
        return ResponseEntity.ok(rateDetails);
    }

    @Operation(summary = "Get a paginated list of rate details", description = "Returns a paginated list of rate details")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "HTTP Status OK"),
            @ApiResponse(responseCode = "400", description = "HTTP Status BAD REQUEST", content = @Content(schema = @Schema(implementation = ErrorResponseDTO.class)))
    })
    @PostMapping("/paginated")
    public ResponseEntity<PageResponseDTO<RateDetailDTO>> getRateDetailsInPages(@Valid @RequestBody PageRequestDTO pageRequestDTO) {
        log.info("Fetching paginated rate details");
        int pageNumber = pageRequestDTO.getPage();
        int pageSize = pageRequestDTO.getPageSize();

        Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);
        PageResponseDTO<RateDetailDTO> response = rateDetailsService.getRateDetailsInPages(pageable);
        log.info("Successfully retrieved paginated rate details");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get a specific rate detail by ID", description = "Returns the rate detail with the specified ID")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "HTTP Status OK"),
            @ApiResponse(responseCode = "404", description = "HTTP Status NOT FOUND", content = @Content(schema = @Schema(implementation = ErrorResponseDTO.class)))
    })
    @GetMapping("/{id}")
    public ResponseEntity<RateDetailDTO> getRateDetailById(@PathVariable Long id) {
        log.info("Fetching rate detail with ID: {}", id);
        RateDetailDTO rateDetail = rateDetailsService.getRateDetailById(id);
        log.info("Retrieved rate detail successfully");
        return ResponseEntity.ok(rateDetail);
    }

    @Operation(summary = "Update a rate detail", description = "Updates the rate detail with the specified ID")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "HTTP Status OK"),
            @ApiResponse(responseCode = "404", description = "HTTP Status NOT FOUND", content = @Content(schema = @Schema(implementation = ErrorResponseDTO.class)))
    })
    @PutMapping("/{id}")
    public ResponseEntity<RateDetailDTO> updateRateDetail(@PathVariable Long id,
            @RequestBody RateDetailDTO rateDetailDTO) {
        log.info("Updating rate detail with ID: {}", id);
        RateDetailDTO updatedDetail = rateDetailsService.updateRateDetail(id, rateDetailDTO);
        log.info("Updated rate detail successfully");
        return ResponseEntity.ok(updatedDetail);
    }

    @Operation(summary = "Delete a rate detail", description = "Deletes the rate detail with the specified ID")
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "HTTP Status NO CONTENT"),
            @ApiResponse(responseCode = "404", description = "HTTP Status NOT FOUND", content = @Content(schema = @Schema(implementation = ErrorResponseDTO.class)))
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseDTO> deleteRateDetail(@PathVariable Long id) {
        log.warn("Deleting rate detail with ID: {}", id);
        rateDetailsService.deleteRateDetail(id);
        log.warn("Rate detail with ID {} deleted successfully", id);
        return ResponseEntity.ok(ResponseDTO.ok(ResponseConstants.MESSAGE_200_DELETE));
    }

    @Operation(summary = "Upload rate details from a file", description = "Uploads rate details from a file for a specific rate package")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "HTTP Status OK"),
            @ApiResponse(responseCode = "400", description = "HTTP Status BAD REQUEST", content = @Content(schema = @Schema(implementation = RateDetailUploadResponse.class))),
            @ApiResponse(responseCode = "500", description = "HTTP Status INTERNAL SERVER ERROR", content = @Content(schema = @Schema(implementation = RateDetailUploadResponse.class)))
    })
    @PostMapping("/upload/{ratePackageId}")
    public ResponseEntity<RateDetailUploadResponse> uploadRateDetails(
            @RequestParam("file") MultipartFile file,
            @RequestParam(name = "type", required = true) String type,
            @PathVariable Long ratePackageId) {

        log.info("Uploading rate details from file: {} for rate package ID: {} with type: {}",
                file.getOriginalFilename(), ratePackageId, type);
        try {
            if ("replace".equalsIgnoreCase(type)) {
                RateDetailUploadResponse response = rateDetailsService.processFile(file, ratePackageId);
                log.info("File processed successfully: {} records processed", response.getRecordsProcessed());
                return ResponseEntity.ok(response);
            }else {
                return ResponseEntity.badRequest().body(
                        new RateDetailUploadResponse(0, "Unsupported type: " + type));
            }
        } catch (ResourceNotFoundException e) {
            // Handle resource not found (like rate package not found) as a 404 error
            log.warn("Resource not found: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
                    new RateDetailUploadResponse(0, "Resource not found: " + e.getMessage()));
        } catch (IllegalArgumentException e) {
            // Handle validation errors (like date format issues) as a 400 error
            log.error("Validation error: {}", e.getMessage());
            return ResponseEntity.badRequest().body(
                    new RateDetailUploadResponse(0, "Validation error: " + e.getMessage()));
        } catch (IOException e) {
            log.error("File processing error: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(
                    new RateDetailUploadResponse(0, "File processing error: " + e.getMessage()));
        } catch (Exception e) {
            log.error("Internal server error: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(
                    new RateDetailUploadResponse(0, "Internal server error: " + e.getMessage()));
        }
    }

@Operation(summary = "Download rate detail template", description = "Download a CSV template for DESTINATION_BASED or SOURCE_DESTINATION_BASED rate details")
@ApiResponses({
        @ApiResponse(responseCode = "200", description = "File downloaded successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid type")
})
@GetMapping("/template/{ratePackageType}")
public ResponseEntity<?> downloadTemplate(@PathVariable("ratePackageType") String ratePackageType) throws IOException {
    String filename;
    if (ratePackageType.equalsIgnoreCase("DESTINATION_BASED")) {
        filename = "destination_based_template.csv";
    } else if (ratePackageType.equalsIgnoreCase("SOURCE_DESTINATION_BASED")) {
        filename = "source_destination_based_template.csv";
    } else {
        return ResponseEntity.badRequest().body("Invalid ratePackageType. Allowed values are DESTINATION_BASED or SOURCE_DESTINATION_BASED.");
    }
    ClassPathResource resource = new ClassPathResource("templates/" + filename);
    byte[] fileBytes = StreamUtils.copyToByteArray(resource.getInputStream());
    return ResponseEntity.ok()
            .header("Content-Disposition", "attachment; filename=" + filename)
            .header("Content-Type", "text/csv")
            .body(fileBytes);
    }

}
