package com.xcess.ocs.service;

import com.xcess.ocs.dto.RatedCdrDTO;
import com.xcess.ocs.entity.SmsRatedCdr;
import com.xcess.ocs.mapper.RateDetailMapper;
import com.xcess.ocs.repository.SmsRatedCdrRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class SmsRatedCdrService {

    private final SmsRatedCdrRepository smsRatedCdrRepository;
    private final RateDetailMapper rateDetailMapper;

    /**
     * Saves an SMS rated CDR to the database
     *
     * @param smsRatedCdr The SMS rated CDR to save
     * @return The saved SMS rated CDR
     */
    @Transactional
    public SmsRatedCdr saveSmsRatedCdr(SmsRatedCdr smsRatedCdr) {
        log.debug("Saving SMS rated CDR: {}", smsRatedCdr.getSmsCdrId());
        return smsRatedCdrRepository.save(smsRatedCdr);
    }

    /**
     * Saves a list of SMS rated CDRs to the database
     *
     * @param smsRatedCdrs The list of SMS rated CDRs to save
     * @return The saved SMS rated CDRs
     */
    @Transactional
    public List<SmsRatedCdr> saveAllSmsRatedCdrs(List<SmsRatedCdr> smsRatedCdrs) {
        log.debug("Saving {} SMS rated CDRs", smsRatedCdrs.size());
        return smsRatedCdrRepository.saveAll(smsRatedCdrs);
    }

    /**
     * Finds an SMS rated CDR by ID
     *
     * @param id The ID of the SMS rated CDR to find
     * @return An Optional containing the SMS rated CDR if found
     */
    @Transactional(readOnly = true)
    public Optional<SmsRatedCdr> findSmsRatedCdrById(Long id) {
        return smsRatedCdrRepository.findById(id);
    }

    /**
     * Finds all SMS rated CDRs
     *
     * @return All SMS rated CDRs
     */
    @Transactional(readOnly = true)
    public List<SmsRatedCdr> findAllSmsRatedCdrs() {
        return smsRatedCdrRepository.findAll();
    }

    /**
     * Maps an SmsRatedCdr entity to a RatedCdrDTO
     *
     * @param smsRatedCdr The SmsRatedCdr entity to map
     * @return The mapped RatedCdrDTO
     */
    public RatedCdrDTO mapToDto(SmsRatedCdr smsRatedCdr) {
        return RatedCdrDTO.builder()
                .ratedCdrId(smsRatedCdr.getSmsCdrId())
                .totalCost(smsRatedCdr.getTotalCost())
                .build();
    }
}