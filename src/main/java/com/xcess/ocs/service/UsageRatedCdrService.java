package com.xcess.ocs.service;

import com.xcess.ocs.dto.RatedCdrDTO;
import com.xcess.ocs.entity.UsageRatedCdr;
import com.xcess.ocs.mapper.RateDetailMapper;
import com.xcess.ocs.repository.UsageRatedCdrRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class UsageRatedCdrService {

    private final UsageRatedCdrRepository usageRatedCdrRepository;
    private final RateDetailMapper rateDetailMapper;

    /**
     * Saves a usage rated CDR to the database
     *
     * @param usageRatedCdr The usage rated CDR to save
     * @return The saved usage rated CDR
     */
    @Transactional
    public UsageRatedCdr saveUsageRatedCdr(UsageRatedCdr usageRatedCdr) {
        log.debug("Saving usage rated CDR: {}", usageRatedCdr.getUsageCdrId());
        return usageRatedCdrRepository.save(usageRatedCdr);
    }

    /**
     * Saves a list of usage rated CDRs to the database
     *
     * @param usageRatedCdrs The list of usage rated CDRs to save
     * @return The saved usage rated CDRs
     */
    @Transactional
    public List<UsageRatedCdr> saveAllUsageRatedCdrs(List<UsageRatedCdr> usageRatedCdrs) {
        log.debug("Saving {} usage rated CDRs", usageRatedCdrs.size());
        return usageRatedCdrRepository.saveAll(usageRatedCdrs);
    }

    /**
     * Finds a usage rated CDR by ID
     *
     * @param id The ID of the usage rated CDR to find
     * @return An Optional containing the usage rated CDR if found
     */
    @Transactional(readOnly = true)
    public Optional<UsageRatedCdr> findUsageRatedCdrById(Long id) {
        return usageRatedCdrRepository.findById(id);
    }

    /**
     * Finds all usage rated CDRs
     *
     * @return All usage rated CDRs
     */
    @Transactional(readOnly = true)
    public List<UsageRatedCdr> findAllUsageRatedCdrs() {
        return usageRatedCdrRepository.findAll();
    }

    /**
     * Maps a UsageRatedCdr entity to a RatedCdrDTO
     *
     * @param usageRatedCdr The UsageRatedCdr entity to map
     * @return The mapped RatedCdrDTO
     */
    public RatedCdrDTO mapToDto(UsageRatedCdr usageRatedCdr) {
        return RatedCdrDTO.builder()
                .ratedCdrId(usageRatedCdr.getUsageCdrId())
                .totalCost(usageRatedCdr.getTotalRate())
                .build();
    }
}