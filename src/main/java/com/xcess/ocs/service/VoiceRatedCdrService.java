package com.xcess.ocs.service;

import com.xcess.ocs.dto.RatedCdrDTO;
import com.xcess.ocs.entity.VoiceRatedCdr;
import com.xcess.ocs.mapper.RateDetailMapper;
import com.xcess.ocs.repository.VoiceRatedCdrRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class VoiceRatedCdrService {

    private final VoiceRatedCdrRepository voiceRatedCdrRepository;
    private final RateDetailMapper rateDetailMapper;

    /**
     * Saves a voice rated CDR to the database
     *
     * @param voiceRatedCdr The voice rated CDR to save
     * @return The saved voice rated CDR
     */
    @Transactional
    public VoiceRatedCdr saveVoiceRatedCdr(VoiceRatedCdr voiceRatedCdr) {
        log.debug("Saving voice rated CDR: {}", voiceRatedCdr.getCallingNumber());
        return voiceRatedCdrRepository.save(voiceRatedCdr);
    }

    /**
     * Saves a list of voice rated CDRs to the database
     *
     * @param voiceRatedCdrs The list of voice rated CDRs to save
     * @return The saved voice rated CDRs
     */
    @Transactional
    public List<VoiceRatedCdr> saveAllVoiceRatedCdrs(List<VoiceRatedCdr> voiceRatedCdrs) {
        log.debug("Saving {} voice rated CDRs", voiceRatedCdrs.size());
        return voiceRatedCdrRepository.saveAll(voiceRatedCdrs);
    }

    /**
     * Finds a voice rated CDR by ID
     *
     * @param id The ID of the voice rated CDR to find
     * @return An Optional containing the voice rated CDR if found
     */
    @Transactional(readOnly = true)
    public Optional<VoiceRatedCdr> findVoiceRatedCdrById(Long id) {
        return voiceRatedCdrRepository.findById(id);
    }

    /**
     * Finds all voice rated CDRs
     *
     * @return All voice rated CDRs
     */
    @Transactional(readOnly = true)
    public List<VoiceRatedCdr> findAllVoiceRatedCdrs() {
        return voiceRatedCdrRepository.findAll();
    }

    /**
     * Maps a VoiceRatedCdr entity to a RatedCdrDTO
     *
     * @param voiceRatedCdr The VoiceRatedCdr entity to map
     * @return The mapped RatedCdrDTO
     */
    public RatedCdrDTO mapToDto(VoiceRatedCdr voiceRatedCdr) {
        return RatedCdrDTO.builder()
                .ratedCdrId(voiceRatedCdr.getVoiceCdrId())
                .callingNumber(voiceRatedCdr.getCallingNumber())
                .calledNumber(voiceRatedCdr.getCalledNumber())
                .startTime(voiceRatedCdr.getStartTime())
                .endTime(voiceRatedCdr.getEndTime())
                .incomingAccountId(voiceRatedCdr.getIncomingAccountId())
                .outgoingAccountId(voiceRatedCdr.getOutgoingAccountId())
                .sourceId(voiceRatedCdr.getSourceId())
                .serviceType(voiceRatedCdr.getServiceType())
                .incomingAppliedRate(voiceRatedCdr.getIncomingAppliedRate())
                .incomingRatePackageId(voiceRatedCdr.getIncomingRatePackageId())
                .incomingRatePackageName(voiceRatedCdr.getIncomingRatePackageName())
                .incomingRateDetailId(voiceRatedCdr.getIncomingRateDetailId())
                .incomingMatchedSourcePrefix(voiceRatedCdr.getIncomingMatchedSourcePrefix())
                .incomingMatchedDestinationPrefix(voiceRatedCdr.getIncomingMatchedDestinationPrefix())
                .isIncomingSourceDestinationMatch(voiceRatedCdr.getIncomingSourceDestinationMatch())
                .outgoingAppliedRate(voiceRatedCdr.getOutgoingAppliedRate())
                .outgoingRatePackageId(voiceRatedCdr.getOutgoingRatePackageId())
                .outgoingRatePackageName(voiceRatedCdr.getOutgoingRatePackageName())
                .outgoingRateDetailId(voiceRatedCdr.getOutgoingRateDetailId())
                .outgoingMatchedSourcePrefix(voiceRatedCdr.getOutgoingMatchedSourcePrefix())
                .outgoingMatchedDestinationPrefix(voiceRatedCdr.getOutgoingMatchedDestinationPrefix())
                .isOutgoingSourceDestinationMatch(voiceRatedCdr.getOutgoingSourceDestinationMatch())
//                .profit(voiceRatedCdr.getProfit())
                .totalCost(voiceRatedCdr.getTotalCost())
                .durationMinutes(voiceRatedCdr.getDurationMinutes())
                .incomingAccountType(voiceRatedCdr.getIncomingAccountType())
                .outgoingAccountType(voiceRatedCdr.getOutgoingAccountType())
//                .ratedAt(voiceRatedCdr.getRatedAt())
                .build();
    }
}