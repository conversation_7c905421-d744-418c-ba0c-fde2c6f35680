package com.xcess.ocs.service;

import com.xcess.ocs.dto.PageResponseDTO;
import com.xcess.ocs.dto.PulseDTO;
import com.xcess.ocs.dto.search.PulseSearchDTO;
import com.xcess.ocs.entity.Pulse;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.entity.ServiceType;
import com.xcess.ocs.entity.UnitType;
import com.xcess.ocs.exception.DuplicateNameException;
import com.xcess.ocs.exception.ForeignReferenceException;
import com.xcess.ocs.mapper.PulseMapper;
import com.xcess.ocs.repository.PulseRepository;
import com.xcess.ocs.repository.RatePackageRepository;
import com.xcess.ocs.util.PaginationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PulseService {
    @Autowired
    private PulseRepository pulseRepository;

    @Autowired
    private RatePackageRepository ratePackageRepository;

    @Autowired
    private PulseMapper pulseMapper;

    public PulseDTO createPulse(PulseDTO pulseDTO) {
        log.debug("Creating new pulse with name: {}", pulseDTO.getPulseName());
        if (pulseRepository.existsByPulseNameAndIsDeletedFalse(pulseDTO.getPulseName())) {
            log.warn("Pulse name already exists: {}", pulseDTO.getPulseName());
            throw new DuplicateNameException("Pulse", pulseDTO.getPulseName());
        }

        // Add validation for unit type
        validateUnitForServiceType(pulseDTO.getServiceType(), pulseDTO.getUnit());

        Pulse pulse = PulseMapper.toEntity(pulseDTO);
        Pulse savedPulse = pulseRepository.save(pulse);
        log.debug("Successfully created pulse with ID: {}", savedPulse.getPulseId());
        return PulseMapper.toDTO(savedPulse);
    }

    public List<PulseDTO> getAllPulses() {
        log.debug("Fetching all pulses");
        List<Pulse> pulses = pulseRepository.findAll();
        List<PulseDTO> pulseDTOs = pulses.stream()
                .map(PulseMapper::toDTO)
                .toList();
        log.debug("Retrieved {} pulses", pulseDTOs.size());
        return pulseDTOs;
    }

    public PageResponseDTO<PulseDTO> getPulsesInPagesByPost(Pageable pageable) {
        log.debug("Fetching pulses in pages");
        Page<Pulse> pulses = pulseRepository.findAll(pageable);
        List<PulseDTO> pulseDTOs = pulses.getContent().stream()
                .map(PulseMapper::toDTO)
                .toList();

        log.debug("Retrieved {} pulses in a page", pulseDTOs.size());
        return PaginationUtils.buildGetResponseDTO(pulseDTOs, pulses);
    }

    public PageResponseDTO<PulseDTO> searchPulses(PulseSearchDTO searchDTO, Pageable pageable) {
        log.debug("Searching pulses with criteria: {}", searchDTO);

        Page<Pulse> pulsePage = pulseRepository.searchPulses(
                searchDTO.getSearchTerm(),
                searchDTO.getServiceType(),
                pageable
        );

        List<PulseDTO> pulses = pulsePage.getContent().stream()
                .map(PulseMapper::toDTO)
                .collect(Collectors.toList());

        log.debug("Found {} pulses matching criteria", pulses.size());
        return PaginationUtils.buildGetResponseDTO(pulses, pulsePage);
    }

    public PulseDTO getPulseById(Long id) {
        log.debug("Fetching pulse with ID: {}", id);
        Pulse pulse = pulseRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Pulse not found with ID: {}", id);
                    return new RuntimeException("Pulse not found");
                });
        return PulseMapper.toDTO(pulse);
    }

    public PulseDTO updatePulse(Long id, PulseDTO pulseDTO) {
        log.debug("Updating pulse with ID: {}", id);
        Pulse pulse = pulseRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Pulse not found with ID: {}", id);
                    return new RuntimeException("Pulse not found");
                });

        if (!pulse.getPulseName().equalsIgnoreCase(pulseDTO.getPulseName()) &&
            pulseRepository.existsByPulseNameAndIsDeletedFalse(pulseDTO.getPulseName())) {
            log.warn("Pulse name already exists: {}", pulseDTO.getPulseName());
            throw new DuplicateNameException("Pulse", pulseDTO.getPulseName());
        }

        // Add validation for unit type
        validateUnitForServiceType(pulseDTO.getServiceType(), pulseDTO.getUnit());

        pulse.setPulseName(pulseDTO.getPulseName());
        pulse.setServiceType(pulseDTO.getServiceType());
        pulse.setUnit(pulseDTO.getUnit());
        pulse.setNoOfUnits(pulseDTO.getNoOfUnits());

        Pulse updatedPulse = pulseRepository.save(pulse);
        log.debug("Successfully updated pulse with ID: {}", id);
        return PulseMapper.toDTO(updatedPulse);
    }

    public void deletePulse(Long id) {
        log.debug("Deleting pulse with ID: {}", id);
        if (!pulseRepository.existsById(id)) {
            log.warn("Attempt to delete non-existent pulse with ID: {}", id);
            throw new RuntimeException("Pulse not found");
        }
        // First, check if there is any active rate package using this pulse
        boolean isReferenced = ratePackageRepository.existsByPulse_PulseIdAndIsDeletedFalse(id);
        if (isReferenced) {
            log.warn("Attempt to soft delete pulse with ID: {} that is referenced by an active rate package", id);
            throw new ForeignReferenceException("Pulse cannot be soft deleted because it is referenced by an active rate package.");
        }
        pulseRepository.deleteById(id);
        log.debug("Successfully deleted pulse with ID: {}", id);
    }

    public int getNumberOfBlocks(int duration, Long ratePackageId) {
        RatePackage ratePackage = ratePackageRepository.findById(ratePackageId)
                .orElseThrow(() -> new RuntimeException("Rate package not found"));
        return switch (ratePackage.getRounding()) {
            case UPPER ->
                    (int) Math.ceil((double) duration / ratePackage.getPulse().getNoOfUnits());
            case LOWER ->
                    (int) Math.floor((double) duration / ratePackage.getPulse().getNoOfUnits());
            case DEFAULT ->
                    (int) Math.round((double) duration / ratePackage.getPulse().getNoOfUnits());
        };
    }

    private void validateUnitForServiceType(ServiceType serviceType, UnitType unit) {
        if (serviceType == null || unit == null) {
            throw new IllegalArgumentException("Service type and unit must not be null");
        }

        boolean isValid = switch (serviceType) {
            case VOICE -> unit == UnitType.SECOND || unit == UnitType.MINUTE;
            case SMS -> unit == UnitType.EVENT;
            case USAGE -> unit == UnitType.KB || unit == UnitType.MB ||
                    unit == UnitType.GB || unit == UnitType.BYTE;
        };

        if (!isValid) {
            String errorMessage = switch (serviceType) {
                case VOICE -> "For VOICE service type, unit must be SECOND or MINUTE";
                case SMS -> "For SMS service type, unit must be EVENT";
                case USAGE -> "For USAGE service type, unit must be KB, MB, GB, or BYTE";
            };
            log.warn(errorMessage);
            throw new IllegalArgumentException(errorMessage);
        }
    }
}
