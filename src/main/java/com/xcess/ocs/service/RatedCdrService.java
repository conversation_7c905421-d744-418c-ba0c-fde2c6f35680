package com.xcess.ocs.service;

import com.xcess.ocs.dto.RatedCdrDTO;
import com.xcess.ocs.entity.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class RatedCdrService {

    private final VoiceRatedCdrService voiceRatedCdrService;
    private final SmsRatedCdrService smsRatedCdrService;
    private final UsageRatedCdrService usageRatedCdrService;

    private RatedCdrServiceContext getContext() {
        return new RatedCdrServiceContext(voiceRatedCdrService, smsRatedCdrService, usageRatedCdrService);
    }

    /**
     * Routes the CDR to the appropriate service based on its type using
     * polymorphism
     */
    @Transactional
    public RatedCdr saveRatedCdr(RatedCdr ratedCdr) {
        return ratedCdr.save(getContext());
    }

    /**
     * Maps a RatedCdr to its DTO representation using polymorphism
     */
    public RatedCdrDTO mapToDto(RatedCdr ratedCdr) {
        return ratedCdr.mapToDto(getContext());
    }
}
