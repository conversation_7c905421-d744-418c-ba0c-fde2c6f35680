package com.xcess.ocs.service;

import com.xcess.ocs.dto.SourceCdrConfigurationDTO;
import com.xcess.ocs.entity.CdrFieldName;
import com.xcess.ocs.entity.ServiceType;
import com.xcess.ocs.entity.SourceCdrConfiguration;
import com.xcess.ocs.entity.SourceConfiguration;
import com.xcess.ocs.mapper.SourceCdrConfigurationMapper;
import com.xcess.ocs.repository.SourceCdrConfigurationRepository;
import com.xcess.ocs.repository.SourceConfigurationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class SourceCdrConfigurationService {

    private final SourceCdrConfigurationRepository sourceCdrConfigurationRepository;
    private final SourceConfigurationRepository sourceConfigurationRepository;
    private final SourceCdrConfigurationMapper sourceCdrConfigurationMapper;

    public List<SourceCdrConfigurationDTO> getConfigurationsBySourceId(Long sourceId) {
        // Verify source exists
        if (!sourceConfigurationRepository.existsById(sourceId)) {
            throw new NoSuchElementException("Source not found with ID: " + sourceId);
        }

        // Retrieve all configurations for the source
        List<SourceCdrConfiguration> configurations = sourceCdrConfigurationRepository
                .findBySourceConfiguration_SourceIdAndIsDeletedFalse(sourceId);

        // Map to DTOs and sort by sequence
        return configurations.stream()
                .map(sourceCdrConfigurationMapper::toDTO)
                .sorted(Comparator.comparing(SourceCdrConfigurationDTO::getSequence))
                .collect(Collectors.toList());
    }

    public SourceCdrConfigurationDTO updateSequence(Long sourceId, Long id, Integer sequence) {
        SourceCdrConfiguration config = sourceCdrConfigurationRepository
                .findBySourceConfiguration_SourceIdAndSourceCdrConfigurationIdAndIsDeletedFalse(sourceId, id)
                .orElseThrow(() -> new NoSuchElementException("Configuration not found for id: " + id));

        // Update the sequence without checking for duplicates in the database
        config.setSequence(sequence);

        // Save and return the updated configuration
        return sourceCdrConfigurationMapper.toDTO(sourceCdrConfigurationRepository.save(config));
    }

    // Add default fields if missing (called internally)
    public void insertDefaultFieldsIfMissing(Long sourceId) {
        SourceConfiguration sourceConfig = sourceConfigurationRepository.findById(sourceId)
                .orElseThrow(() -> new NoSuchElementException("Source not found"));

        if (sourceConfig.getServiceType() == ServiceType.USAGE) {
            Map<CdrFieldName, Integer> defaultSequences = Map.of(
                    CdrFieldName.USER_NUMBER, 1,
                    CdrFieldName.SESSION_START_TIME, 2,
                    CdrFieldName.SESSION_END_TIME, 3,
                    CdrFieldName.DATA_USAGE, 4,
                    CdrFieldName.INCOMING_ACCOUNT_ID, 5,
                    CdrFieldName.OUTGOING_ACCOUNT_ID, 6,
                    CdrFieldName.SERVICE_TYPE, 7);

            for (var entry : defaultSequences.entrySet()) {
                sourceCdrConfigurationRepository
                        .findBySourceConfiguration_SourceIdAndFieldNameAndIsDeletedFalse(sourceId, entry.getKey().name())
                        .or(() -> {
                            SourceCdrConfiguration config = new SourceCdrConfiguration();
                            config.setSourceConfiguration(sourceConfig);
                            config.setFieldName(entry.getKey().name());
                            config.setSequence(entry.getValue());
                            return Optional.of(sourceCdrConfigurationRepository.save(config));
                        });
            }
        }
        if (sourceConfig.getServiceType() == ServiceType.SMS) {
            Map<CdrFieldName, Integer> defaultSequences = Map.of(
                    CdrFieldName.USER_NUMBER, 1,
                    CdrFieldName.MESSAGE_SENT_TIME, 2,
                    CdrFieldName.INCOMING_ACCOUNT_ID, 3,
                    CdrFieldName.OUTGOING_ACCOUNT_ID, 4,
                    CdrFieldName.SERVICE_TYPE, 5);

            for (var entry : defaultSequences.entrySet()) {
                sourceCdrConfigurationRepository
                        .findBySourceConfiguration_SourceIdAndFieldNameAndIsDeletedFalse(sourceId, entry.getKey().name())
                        .or(() -> {
                            SourceCdrConfiguration config = new SourceCdrConfiguration();
                            config.setSourceConfiguration(sourceConfig);
                            config.setFieldName(entry.getKey().name());
                            config.setSequence(entry.getValue());
                            return Optional.of(sourceCdrConfigurationRepository.save(config));
                        });
            }
        }
        if (sourceConfig.getServiceType() == ServiceType.VOICE) {
            Map<CdrFieldName, Integer> defaultSequences = Map.of(
                    CdrFieldName.CALLING_NUMBER, 1,
                    CdrFieldName.CALLED_NUMBER, 2,
                    CdrFieldName.INCOMING_ACCOUNT_ID, 3,
                    CdrFieldName.OUTGOING_ACCOUNT_ID, 4,
                    CdrFieldName.START_TIME, 5,
                    CdrFieldName.END_TIME, 6,
                    CdrFieldName.SERVICE_TYPE, 7);

            for (var entry : defaultSequences.entrySet()) {
                sourceCdrConfigurationRepository
                        .findBySourceConfiguration_SourceIdAndFieldNameAndIsDeletedFalse(sourceId, entry.getKey().name())
                        .or(() -> {
                            SourceCdrConfiguration config = new SourceCdrConfiguration();
                            config.setSourceConfiguration(sourceConfig);
                            config.setFieldName(entry.getKey().name());
                            config.setSequence(entry.getValue());
                            return Optional.of(sourceCdrConfigurationRepository.save(config));
                        });
            }
        }
    }
}
