package com.xcess.ocs.dto;

import com.xcess.ocs.entity.ServiceType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Represents a rated CDR with all rating information.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RatedCdrDTO {
    private Long ratedCdrId;
    private String callingNumber;
    private String calledNumber;
    private String startTime;
    private String endTime;
    private String incomingAccountId;
    private String outgoingAccountId;
    private Long sourceId;
    private ServiceType serviceType;

    // Incoming account rate information
    private BigDecimal incomingAppliedRate;
    private Long incomingRatePackageId;
    private String incomingRatePackageName;
    private Long incomingRateDetailId;
    private String incomingMatchedSourcePrefix;
    private String incomingMatchedDestinationPrefix;
    private Boolean isIncomingSourceDestinationMatch;

    // Outgoing account rate information
    private BigDecimal outgoingAppliedRate;
    private Long outgoingRatePackageId;
    private String outgoingRatePackageName;
    private Long outgoingRateDetailId;
    private String outgoingMatchedSourcePrefix;
    private String outgoingMatchedDestinationPrefix;
    private Boolean isOutgoingSourceDestinationMatch;

    // Cost and profit calculations
    private BigDecimal profit;
    private BigDecimal totalCost;
    private BigDecimal durationMinutes;

    // Account types
    private String incomingAccountType;
    private String outgoingAccountType;

    // Timestamp when rating was applied
    private LocalDateTime ratedAt;

    // Voice-specific fields
    private Integer messageSegments;

    // Usage-specific fields
    private Long dataVolumeKb;
}
