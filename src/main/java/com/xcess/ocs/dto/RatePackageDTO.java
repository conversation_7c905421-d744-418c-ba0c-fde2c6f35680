package com.xcess.ocs.dto;

import com.xcess.ocs.entity.*;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "7. Rate Package", description = "Details of a rate package")
public class RatePackageDTO {
    @Schema(description = "Rate package ID", accessMode = Schema.AccessMode.READ_ONLY)
    private Long ratePackageId;

    @Schema(description = "Name of the rate package", example = "Basic Package")
    @NotBlank(message = "Package name is required")
    @Size(max = 100, message = "Package name cannot exceed 100 characters")
    private String packageName;

    @Schema(description = "Description of the rate package", example = "This is a basic rate package")
    @NotBlank(message = "Package description is required")
    @Size(max = 255, message = "Package description cannot exceed 255 characters")
    private String packageDesc;

    @Schema(description = "Type of the rate package", example = "SELLING")
    @NotBlank(message = "Package type is required")
    @Pattern(regexp = "^(SELLING|BUYING)$", message = "Package type must be one of: SELLING, BUYING")
    private Type type;

    @Schema(description = "Service type of the rate package", example = "VOICE")
    @NotBlank(message = "Service type is required")
    @Pattern(regexp = "^(VOICE|SMS|USAGE)$", message = "Service type must be one of: VOICE, SMS, USAGE")
    private ServiceType serviceType;

    @Schema(description = "Type of rate package (DESTINATION_BASED or SOURCE_DESTINATION_BASED)", example = "DESTINATION_BASED")
//    @NotNull(message = "Rate package type is required")
    private RatePackageType ratePackageType;

    @Schema(description = "Pulse ID for reference to Pulse", example = "1")
    @NotNull(message = "Pulse ID is required")
    private Long pulseId;

    @Schema(description = "Name of the pulse", example = "Basic Pulse", accessMode = Schema.AccessMode.READ_ONLY)
    private String pulseName;

    @Schema(description = "Rounding type of the rate package", example = "UPPER")
    @NotBlank(message = "Rounding method is required")
    @Pattern(regexp = "^(UP|DOWN|NEAREST)$", message = "Rounding must be one of: UP, DOWN, NEAREST")
    private Rounding rounding;

    @Schema(description = "Price Rounding type of the rate package", example = "UPPER")
    @NotBlank(message = "Price rounding method is required")
    @Pattern(regexp = "^(UP|DOWN|NEAREST)$", message = "Price rounding must be one of: UP, DOWN, NEAREST")
    private Rounding priceRounding;

    @Schema(description = "Rate details of the rate package", accessMode= Schema.AccessMode.READ_ONLY)
    @Valid
    private List<RateDetailDTO> rate_details;

    @Schema(description = "Tagging of the rate package, e.g., 'master'", example = "master")
    private String subtype;  // This will hold values like 'master'.

    @Schema(description = "Rate type of the rate package", example = "Flat")
    private RateType rateType;

    @Schema(description = "Rate of the rate package", example = "20")
    private Double rate;

    @Schema(description = "Tier rates of the rate package", accessMode= Schema.AccessMode.READ_ONLY)
    private List<TierRate>tierRate;
}