package com.xcess.ocs.ratingengine.util;

import com.xcess.ocs.entity.RateDetails;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Trie structure for a single RatePackage, supporting both source-destination and destination-only lookups.
 */
public class RateTrie {
    // Trie for source-destination based rates: sourcePrefix -> (destinationPrefix -> List<RateDetails>)
    private RadixTrie<RadixTrie<List<RateDetails>>> sourceDestTrie = new RadixTrie<>();
    // Trie for destination-only based rates: destinationPrefix -> List<RateDetails>
    private RadixTrie<List<RateDetails>> destOnlyTrie = new RadixTrie<>();

    /**
     * Insert a RateDetails into the appropriate trie.
     */
    public void insert(RateDetails rateDetails) {
        String destPrefix = rateDetails.getDestinationPrefix();
        String srcPrefix = rateDetails.getSourcePrefix();
        if (srcPrefix != null && !srcPrefix.trim().isEmpty()) {
            // Source-Destination based
            // RadixTrie<List<RateDetails>> destTrie = sourceDestTrie.searchLongestPrefix(srcPrefix);
            if (sourceDestTrie == null) {
                sourceDestTrie = new RadixTrie<>();
                // sourceDestTrie.insert(srcPrefix, destTrie);
            }
            RadixTrie<List<RateDetails>> destTrie = sourceDestTrie.searchLongestPrefix(srcPrefix);
            // List<RateDetails> detailsList = destTrie.searchLongestPrefix(destPrefix);
            if (destTrie == null) {
                destTrie = new RadixTrie<>();
                sourceDestTrie.insert(srcPrefix, destTrie);
            }
            List<RateDetails> detailsList = destTrie.searchLongestPrefix(destPrefix);
            if (detailsList == null) {
                detailsList = new ArrayList<>();
                destTrie.insert(destPrefix, detailsList);
            }
            detailsList.add(rateDetails);
        } else {
            // Destination-only based
            // List<RateDetails> detailsList = destOnlyTrie.searchLongestPrefix(destPrefix);
            if (destOnlyTrie == null) {
                destOnlyTrie = new RadixTrie<>();
                // destOnlyTrie.insert(destPrefix, detailsList);
            }
            List<RateDetails> detailsList = destOnlyTrie.searchLongestPrefix(destPrefix);
            if (detailsList == null) {
                detailsList = new ArrayList<>();
                destOnlyTrie.insert(destPrefix, detailsList);
            }
            detailsList.add(rateDetails);
        }
    }

    /**
     * Find the best matching RateDetails for the given source, destination, and time.
     * First tries source-destination, then destination-only.
     */
    public RateDetails findBestRate(String source, String destination, LocalDateTime callTime) {
        // 1. Try source-destination
        if(sourceDestTrie != null) {
        RadixTrie<List<RateDetails>> destTrie = sourceDestTrie.searchLongestPrefix(source);
        if (destTrie != null) {
            List<RateDetails> detailsList = destTrie.searchLongestPrefix(destination);
            RateDetails match = findValidByTime(detailsList, callTime);
            if (match != null) return match;
        }
    }
        if(destOnlyTrie != null) {
        // 2. Try destination-only 
        List<RateDetails> detailsList = destOnlyTrie.searchLongestPrefix(destination);
        return findValidByTime(detailsList, callTime);
        }
        return null;
    }

    /**
     * Helper: Find a RateDetails in the list that is valid for the given time.
     */
    private RateDetails findValidByTime(List<RateDetails> detailsList, LocalDateTime callTime) {
        if (detailsList == null) return null;
        for (RateDetails rd : detailsList) {
            if ((rd.getStartTime() == null || !callTime.isBefore(rd.getStartTime())) &&
                (rd.getEndTime() == null || !callTime.isAfter(rd.getEndTime()))) {
                return rd;
            }
        }
        return null;
    }
} 