package com.xcess.ocs.ratingengine.service;

import com.xcess.ocs.entity.RatePackage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service responsible for initializing the RadixTrie data structures with rate packages.
 * This service is used during cache initialization and refresh operations.
 */
@Service
public class TrieInitializationService {
    private final RateTrieService rateTrieService;

    @Autowired
    public TrieInitializationService(RateTrieService rateTrieService) {
        this.rateTrieService = rateTrieService;
    }

    /**
     * Initialize all tries for the given list of rate packages.
     * @param ratePackages The rate packages to initialize tries for
     */
    public void initialize(List<RatePackage> ratePackages) {
        rateTrieService.initializeTries(ratePackages);
    }
} 