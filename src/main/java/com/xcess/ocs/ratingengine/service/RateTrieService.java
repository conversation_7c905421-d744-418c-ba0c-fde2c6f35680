package com.xcess.ocs.ratingengine.service;

import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.ratingengine.util.RateTrie;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service that manages RadixTries for rate lookups.
 * This service maintains a map of rate package IDs to their corresponding RadixTries,
 * providing O(k) lookup performance where k is the prefix length.
 */
@Service
public class RateTrieService {
    private static final Logger log = LoggerFactory.getLogger(RateTrieService.class);
    
    /**
     * Map of rate package IDs to their corresponding RadixTries.
     * This is the core in-memory data structure that enables fast prefix lookups.
     */
    private final Map<Long, RateTrie> rateTrieMap = new ConcurrentHashMap<>();

    /**
     * Initialize tries for all rate packages.
     * This method is called at application startup and during cache refresh events.
     * 
     * @param ratePackages List of rate packages to initialize tries for
     */
    public void initializeTries(List<RatePackage> ratePackages) {
        log.info("Initializing rate tries for {} packages", ratePackages.size());
        rateTrieMap.clear();
        
        for (RatePackage pkg : ratePackages) {
            log.debug("Building trie for package: {} (ID: {})", 
                      pkg.getPackageName(), pkg.getRatePackageId());
            
            RateTrie trie = new RateTrie();
            for (RateDetails details : pkg.getRateDetails()) {
                trie.insert(details);
            }
            
            rateTrieMap.put(pkg.getRatePackageId(), trie);
        }
        
        log.info("Rate tries initialization complete for {} packages", ratePackages.size());
    }

    /**
     * Find the best rate for a call based on source, destination, and time.
     * This is the primary method used during CDR rating.
     **/
    public RateDetails findBestRate(Long ratePackageId, String source, String destination, LocalDateTime callTime) {
        // Add null safety check
        if (ratePackageId == null) {
            log.error("Null ratePackageId provided. Source={}, Destination={}", source, destination);
            return null;
        }
        
        RateTrie trie = rateTrieMap.get(ratePackageId);
        if (trie == null) {
            log.warn("No rate trie found for package ID: {}", ratePackageId);
            return null;
        }
        
        log.debug("Looking up rate: package={}, source={}, destination={}, time={}", 
                 ratePackageId, source, destination, callTime);
                 
        return trie.findBestRate(source, destination, callTime);
    }
    
    /**
     * Check if a rate package is loaded in memory.
     * 
     * @param ratePackageId The rate package ID to check
     * @return true if the package is loaded, false otherwise
     */
    public boolean isPackageLoaded(Long ratePackageId) {
        return ratePackageId != null && rateTrieMap.containsKey(ratePackageId);
    }
    
    /**
     * Get the number of rate packages currently loaded.
     * 
     * @return Number of rate packages loaded
     */
    public int getLoadedPackageCount() {
        return rateTrieMap.size();
    }
} 