package com.xcess.ocs.ratingengine.service;

import com.xcess.ocs.entity.*;
import com.xcess.ocs.repository.AccountRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service responsible for finding rate packages based on account information.
 * This service traverses the entity relationships from Account → ProductPlan →
 * RatePackageGroup → RatePackage to find the appropriate rate package for a
 * given call.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class AccountRateService {
    private final AccountRepository accountRepository;

    /**
     * Find the appropriate rate package for an account based on call time and
     * service type.
     * This method traverses entity relationships to determine the correct rate
     * package
     * that applies to the given account at the specified time for the specified
     * service type.
     * Results are cached for performance, with the cache key including accountId,
     * callTime, and serviceType.
     */
    @Cacheable(value = "accountRatePackage", key = "{#accountId, #callTime, #serviceType}")
    @Transactional(readOnly = true)
    public RatePackage findRatePackageForAccount(String accountId, LocalDateTime callTime, ServiceType serviceType) {
        if (accountId == null) {
            log.warn("Null accountId provided to findRatePackageForAccount");
            return null;
        }

        try {
            // Find account by account code
            Optional<Account> accountOpt = accountRepository.findByAccountCodeAndIsDeletedFalse(accountId);

            if (accountOpt.isEmpty()) {
                log.warn("Account not found with code: {}", accountId);
                return null;
            }

            Account account = accountOpt.get();
            log.debug("Found account: {}, partner type: {}", account.getAccountCode(), account.getPartnerType());

            // Step 2: Get the product plan for this account
            ProductPlan productPlan = account.getProductPlan();
            if (productPlan == null) {
                log.warn("No product plan associated with account: {}", accountId);
                return null;
            }
            log.debug("Found product plan: {}", productPlan.getName());

            // Step 3: Find applicable rate package group for the call time
            RatePackageGroup applicableGroup = findApplicableRatePackageGroup(productPlan, callTime);
            if (applicableGroup == null) {
                log.warn("No applicable rate package group found for account {} at time {}", accountId, callTime);
                return null;
            }
            log.debug("Found applicable rate package group: {}", applicableGroup.getName());

            // Step 4: Find appropriate rate package based on account type, time, and
            // service type
            RatePackage ratePackage = findAppropriateRatePackage(applicableGroup, account.getPartnerType(), callTime,
                    serviceType);
            if (ratePackage == null) {
                log.warn("No rate package found for account {} with type {} and service type {}",
                        accountId, account.getPartnerType(), serviceType);
                return null;
            }

            log.debug("Found rate package {} for account {} and service type {}",
                    ratePackage.getPackageName(), accountId, serviceType);
            return ratePackage;
        } catch (Exception e) {
            log.error("Error finding rate package for account {}: {}", accountId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Find the applicable rate package group based on the product plan and call
     * time.
     * This traverses from ProductPlan to RatePackageGroup through their
     * associations.
     */
    private RatePackageGroup findApplicableRatePackageGroup(ProductPlan productPlan, LocalDateTime callTime) {
        List<ProductPlanAssociation> associations = productPlan.getRatePackageGroups();
        if (associations == null || associations.isEmpty()) {
            log.warn("No rate package group associations found for product plan: {}", productPlan.getName());
            return null;
        }

        // Find association that's active at call time
        Optional<ProductPlanAssociation> activeAssociation = associations.stream()
                .filter(assoc -> isTimeInRange(callTime, assoc.getStartTime(), assoc.getEndTime()))
                .findFirst();

        if (activeAssociation.isPresent()) {
            log.debug("Found active association for call time: {}", callTime);
            return activeAssociation.get().getRatePackageGroup();
        } else {
            // No time-specific match, return the first group's rate package group
            log.debug("No time-specific association found, using default");
            return associations.get(0).getRatePackageGroup();
        }
    }

    // Check if a time is within a range.
    private boolean isTimeInRange(LocalDateTime time, LocalDateTime start, LocalDateTime end) {
        return (time.isEqual(start) || time.isAfter(start)) &&
                (time.isBefore(end) || time.isEqual(end));
    }

    /**
     * Find appropriate rate package based on account type, time, and service type.
     * This traverses from RatePackageGroup to RatePackage through their
     * associations.
     */
    private RatePackage findAppropriateRatePackage(RatePackageGroup group, String accountType,
            LocalDateTime callTime, ServiceType serviceType) {
        List<RatePackageAssociation> associations = group.getRatePackageAssociations();
        if (associations == null || associations.isEmpty()) {
            log.warn("No rate package associations found for group: {}", group.getName());
            return null;
        }

        // First filter by time range
        List<RatePackageAssociation> activeAssociations = associations.stream()
                .filter(assoc -> isTimeInRange(callTime, assoc.getStartTime(), assoc.getEndTime()))
                .collect(Collectors.toList());

        if (activeAssociations.isEmpty()) {
            // If no time-specific matches, use all associations
            log.debug("No time-specific rate package associations found, using all");
            activeAssociations = associations;
        }

        // Then filter by service type if specified
        if (serviceType != null) {
            List<RatePackageAssociation> serviceTypeAssociations = activeAssociations.stream()
                    .filter(assoc -> serviceType.equals(assoc.getRatePackage().getServiceType()))
                    .collect(Collectors.toList());

            if (!serviceTypeAssociations.isEmpty()) {
                activeAssociations = serviceTypeAssociations;
            } else {
                log.debug("No rate packages found for service type {}, using all", serviceType);
            }
        }

        // Then find package matching account type
        Optional<RatePackageAssociation> matchingAssociation = activeAssociations.stream()
                .filter(assoc -> isPackageApplicableForType(assoc.getRatePackage(), accountType))
                .findFirst();

        return matchingAssociation.map(RatePackageAssociation::getRatePackage)
                .orElse(activeAssociations.isEmpty() ? null : activeAssociations.get(0).getRatePackage());
    }

    /**
     * Check if a rate package is applicable for an account type.
     */
    private boolean isPackageApplicableForType(RatePackage ratePackage, String accountType) {
        if (accountType == null || ratePackage == null || ratePackage.getType() == null) {
            return false;
        }

        String packageTypeCode = ratePackage.getType().name();
        return packageTypeCode.equals(accountType) || packageTypeCode.equals("BOTH");
    }

    /**
     * Get the account type (VENDOR or CUSTOMER) for a given account ID.
     */
    @Transactional(readOnly = true)
    public String getAccountType(String accountId) {
        if (accountId == null) {
            log.warn("Null accountId provided to getAccountType");
            return null;
        }

        try {
            // Find account by account code
            Optional<Account> accountOpt = accountRepository.findByAccountCodeAndIsDeletedFalse(accountId);

            if (accountOpt.isEmpty()) {
                log.warn("Account not found with code: {}", accountId);
                return null;
            }

            Account account = accountOpt.get();
            log.debug("Found account: {}, partner type: {}", account.getAccountCode(), account.getPartnerType());

            return account.getPartnerType();
        } catch (Exception e) {
            log.error("Error finding account type for account {}: {}", accountId, e.getMessage(), e);
            return null;
        }
    }
}