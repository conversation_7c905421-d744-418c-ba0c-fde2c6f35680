package com.xcess.ocs.ratingengine.service;

import com.xcess.ocs.dto.RatedCdrDTO;
import com.xcess.ocs.entity.*;
import com.xcess.ocs.service.PulseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;

/**
 * Service responsible for integrating CDR processing with the rating engine.
 * This service acts as the bridge between Kafka CDR data and the
 * RadixTrie-based
 * rating algorithm, handling the complete flow from CDR receipt to rating
 * application.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CdrRatingIntegrationService {

    private final RateLookupService rateLookupService;
    private final AccountRateService accountRateService;
    private final PulseService pulseService;
    // Common timestamp formats to try when parsing CDR timestamps
    private static final DateTimeFormatter[] TIMESTAMP_FORMATTERS = {
            DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss"), // Format: 14-05-2025 02:23:34
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"),
            DateTimeFormatter.ISO_LOCAL_DATE_TIME
    };

    /**
     * Process and rate a CDR from Kafka before database storage.
     * This method implements the complete flow:
     * 1. Create entity from DTO
     * 2. Derive rate package from account information
     * 3. Apply rate lookup using the RadixTrie
     * 4. Return appropriate entity based on rating success
     * 
     * @param cdrDto      The CDR data from Kafka
     * @param sourceId    The source ID for this CDR
     * @return Either a RatedCdr (if successfully rated) or an UnRatedCdr (if rating
     *         failed)
     */
    public Object processAndRateCdr(RatedCdrDTO cdrDto, Long sourceId) {
        switch (cdrDto.getServiceType()) {
            case VOICE: {
                log.debug("Processing CDR for rating: calling={}, called={}, sourceId={}",
                        cdrDto.getCallingNumber(), cdrDto.getCalledNumber(), sourceId);

                // Extract basic CDR information
                String callingNumber = cdrDto.getCallingNumber();
                String calledNumber = cdrDto.getCalledNumber();
                String startTime = cdrDto.getStartTime();
                String endTime = cdrDto.getEndTime();
                String incomingAccountId = cdrDto.getIncomingAccountId();
                String outgoingAccountId = cdrDto.getOutgoingAccountId();
                ServiceType st = cdrDto.getServiceType();

                // Calculate duration
                long durationSeconds = calculateDurationInSeconds(startTime, endTime);
                BigDecimal durationMinutes = BigDecimal.valueOf(durationSeconds).divide(BigDecimal.valueOf(60), 2,
                        BigDecimal.ROUND_HALF_UP);

                try {
                    // Step 1: Parse call timestamp for rating validation
                    LocalDateTime callTimestamp = parseTimestamp(startTime);
                    if (callTimestamp == null) {
                        log.error("Timestamp parsing failed for CDR: calling={}, called={}, startTime='{}'",
                                callingNumber, calledNumber, startTime);

                        return UnRatedCdr.createFromBasicInfo(
                                callingNumber, calledNumber, startTime, endTime,
                                incomingAccountId, outgoingAccountId, sourceId, BigDecimal.valueOf(durationSeconds),
                                "INVALID_TIMESTAMP", null, null, st);
                    }

                    // Step 2: Get account types for both accounts
                    String incomingAccountType = accountRateService.getAccountType(incomingAccountId);
                    String outgoingAccountType = accountRateService.getAccountType(outgoingAccountId);

                    // Step 3: Process incoming account rating
                    RateResult incomingRateResult = processAccountRating(
                            incomingAccountId, callingNumber, calledNumber, callTimestamp, st);

                    // Step 4: Process outgoing account rating
                    RateResult outgoingRateResult = processAccountRating(
                            outgoingAccountId, callingNumber, calledNumber, callTimestamp, st);

                    // Step 5: Create appropriate entity based on rating results
                    if (incomingRateResult.success && outgoingRateResult.success) {
                        VoiceRatedCdr ratedCdr = new VoiceRatedCdr();
                        ratedCdr.setCallingNumber(callingNumber);
                        ratedCdr.setCalledNumber(calledNumber);
                        ratedCdr.setStartTime(startTime);
                        ratedCdr.setEndTime(endTime);
                        ratedCdr.setIncomingAccountId(incomingAccountId);
                        ratedCdr.setOutgoingAccountId(outgoingAccountId);
                        ratedCdr.setSourceId(sourceId);
                        ratedCdr.setDurationMinutes(BigDecimal.valueOf(durationSeconds / 60.0));

                        // Determine source-destination matches for both accounts
                        boolean isIncomingSourceDestMatch = incomingRateResult.rateDetails.getSourcePrefix() != null &&
                                !incomingRateResult.rateDetails.getSourcePrefix().trim().isEmpty();
                        boolean isOutgoingSourceDestMatch = outgoingRateResult.rateDetails.getSourcePrefix() != null &&
                                !outgoingRateResult.rateDetails.getSourcePrefix().trim().isEmpty();

                        // Set rating information for incoming account
                        ratedCdr.setIncomingAppliedRate(BigDecimal.valueOf(incomingRateResult.rateDetails.getRate()));
                        ratedCdr.setIncomingRatePackageId(incomingRateResult.ratePackage.getRatePackageId());
                        ratedCdr.setIncomingRatePackageName(incomingRateResult.ratePackage.getPackageName());
                        ratedCdr.setIncomingRateDetailId(incomingRateResult.rateDetails.getRateDetailsId());
                        ratedCdr.setIncomingMatchedSourcePrefix(incomingRateResult.rateDetails.getSourcePrefix());
                        ratedCdr.setIncomingMatchedDestinationPrefix(
                                incomingRateResult.rateDetails.getDestinationPrefix());
                        ratedCdr.setIncomingSourceDestinationMatch(isIncomingSourceDestMatch);

                        // Set rating information for outgoing account
                        ratedCdr.setOutgoingAppliedRate(BigDecimal.valueOf(outgoingRateResult.rateDetails.getRate()));
                        ratedCdr.setOutgoingRatePackageId(outgoingRateResult.ratePackage.getRatePackageId());
                        ratedCdr.setOutgoingRatePackageName(outgoingRateResult.ratePackage.getPackageName());
                        ratedCdr.setOutgoingRateDetailId(outgoingRateResult.rateDetails.getRateDetailsId());
                        ratedCdr.setOutgoingMatchedSourcePrefix(outgoingRateResult.rateDetails.getSourcePrefix());
                        ratedCdr.setOutgoingMatchedDestinationPrefix(
                                outgoingRateResult.rateDetails.getDestinationPrefix());
                        ratedCdr.setOutgoingSourceDestinationMatch(isOutgoingSourceDestMatch);

                        // Set account types
                        ratedCdr.setIncomingAccountType(incomingAccountType);
                        ratedCdr.setOutgoingAccountType(outgoingAccountType);

                        // FLAT rate logic for incoming account
                        if (incomingRateResult.rateDetails.getRateType() == RateType.FLAT) {
                            Pulse pulse = incomingRateResult.ratePackage.getPulse();
                            int blocks;
                            if (pulse.getUnit() == UnitType.SECOND) {
                                blocks = pulseService.getNumberOfBlocks((int) durationSeconds,
                                        incomingRateResult.ratePackage.getRatePackageId());
                            } else if (pulse.getUnit() == UnitType.MINUTE) {
                                int durationMinutes = (int) Math.ceil(durationSeconds / 60.0);
                                blocks = pulseService.getNumberOfBlocks(durationMinutes,
                                        incomingRateResult.ratePackage.getRatePackageId());
                            } else {
                                blocks = 0; // or handle other units
                            }
                            BigDecimal cost = BigDecimal.valueOf(blocks)
                                    .multiply(BigDecimal.valueOf(incomingRateResult.rateDetails.getRate()));
                            ratedCdr.setIncomingAppliedRate(cost);
                        }
                        // FLAT rate logic for outgoing account
                        if (outgoingRateResult.rateDetails.getRateType() == RateType.FLAT) {
                            Pulse pulse = outgoingRateResult.ratePackage.getPulse();
                            int blocks;
                            if (pulse.getUnit() == UnitType.SECOND) {
                                blocks = pulseService.getNumberOfBlocks((int) durationSeconds,
                                        outgoingRateResult.ratePackage.getRatePackageId());
                            } else if (pulse.getUnit() == UnitType.MINUTE) {
                                int durationMinutes = (int) Math.ceil(durationSeconds / 60.0);
                                blocks = pulseService.getNumberOfBlocks(durationMinutes,
                                        outgoingRateResult.ratePackage.getRatePackageId());
                            } else {
                                blocks = 0; // or handle other units
                            }
                            BigDecimal cost = BigDecimal.valueOf(blocks)
                                    .multiply(BigDecimal.valueOf(outgoingRateResult.rateDetails.getRate()));
                            ratedCdr.setOutgoingAppliedRate(cost);
                        }

                        log.info(
                                "Successfully rated CDR: calling={}, called={}, incomingRate={}, outgoingRate={}",
                                callingNumber, calledNumber,
                                ratedCdr.getIncomingAppliedRate(),
                                ratedCdr.getOutgoingAppliedRate());

                        return ratedCdr;
                    } else {
                        // At least one rate lookup failed - create UnRatedCdr
                        String failureReason;
                        Long attemptedPackageId = null;
                        String attemptedPackageName = null;

                        if (!incomingRateResult.success && !outgoingRateResult.success) {
                            failureReason = "NO_MATCHING_RATE_FOR_BOTH_ACCOUNTS";
                        } else if (!incomingRateResult.success) {
                            failureReason = "NO_MATCHING_RATE_FOR_INCOMING_ACCOUNT";
                            if (outgoingRateResult.ratePackage != null) {
                                attemptedPackageId = outgoingRateResult.ratePackage.getRatePackageId();
                                attemptedPackageName = outgoingRateResult.ratePackage.getPackageName();
                            }
                        } else {
                            failureReason = "NO_MATCHING_RATE_FOR_OUTGOING_ACCOUNT";
                            if (incomingRateResult.ratePackage != null) {
                                attemptedPackageId = incomingRateResult.ratePackage.getRatePackageId();
                                attemptedPackageName = incomingRateResult.ratePackage.getPackageName();
                            }
                        }

                        log.warn("Rating failed: {}, calling={}, called={}, duration={} seconds",
                                failureReason, callingNumber, calledNumber, durationSeconds);

                        return UnRatedCdr.createFromBasicInfo(
                                callingNumber, calledNumber, startTime, endTime,
                                incomingAccountId, outgoingAccountId, sourceId, BigDecimal.valueOf(durationSeconds),
                                failureReason, attemptedPackageId, attemptedPackageName, st);
                    }
                } catch (Exception e) {
                    // Handle any errors during rating
                    log.error("Failed to rate CDR: calling={}, called={}, error={}",
                            callingNumber, calledNumber, e.getMessage(), e);

                    return UnRatedCdr.createFromBasicInfo(
                            callingNumber, calledNumber, startTime, endTime,
                            incomingAccountId, outgoingAccountId, sourceId, BigDecimal.valueOf(durationSeconds),
                            "RATING_ERROR: " + e.getMessage(), null, null, st);
                }
            }
            case SMS: {
                // TODO: Implement SMS logic
                break;
            }
            case USAGE: {
                // TODO: Implement USAGE logic
                break;
            }
            default:
                throw new IllegalArgumentException("Unknown service type: " + cdrDto.getServiceType());
        }
        return null; // Should never reach here
    }

    /**
     * Process rating for a single account
     * 
     * @param accountId     The account ID to process
     * @param callingNumber The calling number
     * @param calledNumber  The called number
     * @param callTimestamp The call timestamp
     * @param serviceType   The service type (VOICE, SMS, USAGE)
     * @return A RateResult containing success status and rate information
     */
    private RateResult processAccountRating(String accountId, String callingNumber,
            String calledNumber, LocalDateTime callTimestamp, ServiceType serviceType) {

        RateResult result = new RateResult();

        // Find rate package for the account
        RatePackage ratePackage = accountRateService.findRatePackageForAccount(accountId, callTimestamp, serviceType);
        result.ratePackage = ratePackage;

        if (ratePackage == null) {
            log.warn("No rate package found for account: {} and service type: {}", accountId, serviceType);
            return result;
        }

        // Use the RateLookupService to find the best rate
        RateDetails bestRate = rateLookupService.findBestRate(
                ratePackage.getRatePackageId(), // Derived rate package ID
                callingNumber, // Source number
                calledNumber, // Destination number
                callTimestamp // Call timestamp
        );

        if (bestRate != null) {
            result.success = true;
            result.rateDetails = bestRate;

            // Log whether this was a source-destination match or destination-only match
            boolean isSourceDestMatch = bestRate.getSourcePrefix() != null &&
                    !bestRate.getSourcePrefix().trim().isEmpty();

            log.debug(
                    "Found rate for account {}: rate={}, package={}, sourcePrefix={}, destPrefix={}, isSourceDestMatch={}",
                    accountId, bestRate.getRate(), ratePackage.getPackageName(),
                    bestRate.getSourcePrefix(), bestRate.getDestinationPrefix(), isSourceDestMatch);
        } else {
            log.warn("No matching rate found for account {} with package {}",
                    accountId, ratePackage.getPackageName());
        }

        return result;
    }

    /**
     * Calculate call duration from start and end times.
     * 
     * @param startTimeStr Start time string
     * @param endTimeStr   End time string
     * @return Duration in seconds, or 0 if calculation fails
     */
    public long calculateDurationInSeconds(String startTimeStr, String endTimeStr) {
        try {
            LocalDateTime startTime = parseTimestamp(startTimeStr);
            LocalDateTime endTime = parseTimestamp(endTimeStr);

            if (startTime != null && endTime != null) {
                return ChronoUnit.SECONDS.between(startTime, endTime);
            }
        } catch (Exception e) {
            log.error("Error calculating duration: error={}", e.getMessage());
        }
        return 0;
    }

    /**
     * Parse timestamp string using multiple common formats.
     * This method tries several common timestamp formats to handle various input
     * formats.
     * 
     * @param timestampStr The timestamp string to parse
     * @return Parsed LocalDateTime, or null if parsing fails
     */
    private LocalDateTime parseTimestamp(String timestampStr) {
        if (timestampStr == null || timestampStr.trim().isEmpty()) {
            return null;
        }

        String cleanTimestamp = timestampStr.trim();

        for (DateTimeFormatter formatter : TIMESTAMP_FORMATTERS) {
            try {
                return LocalDateTime.parse(cleanTimestamp, formatter);
            } catch (DateTimeParseException e) {
                // Try next format
            }
        }

        log.warn("Failed to parse timestamp: '{}' with any known format", timestampStr);
        return null;
    }

    /**
     * Helper class to store the result of a rate lookup operation
     */
    private static class RateResult {
        boolean success = false;
        RatePackage ratePackage;
        RateDetails rateDetails;
    }
}