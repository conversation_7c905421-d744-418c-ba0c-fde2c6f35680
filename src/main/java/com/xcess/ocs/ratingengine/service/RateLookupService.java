package com.xcess.ocs.ratingengine.service;

import com.xcess.ocs.entity.RateDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * Service that provides high-level rate lookup functionality.
 * This is a facade over the RateTrieService, simplifying the rate lookup interface.
 */
@Service
public class RateLookupService {
    private final RateTrieService rateTrieService;

    public RateLookupService(RateTrieService rateTrieService) {
        this.rateTrieService = rateTrieService;
    }


    /**
     * Find the best matching RateDetails for a given rate package, source, destination, and call timed
     */
    public RateDetails findBestRate(Long ratePackageId, String source, String destination, LocalDateTime callTime) {
        return rateTrieService.findBestRate(ratePackageId, source, destination, callTime);
    }
} 